import cv2
import numpy as np
import pytesseract
from pdf2image import convert_from_path
from PIL import Image, ImageEnhance, ImageFilter
import os
import re
import subprocess
import json
import logging
from datetime import datetime
from typing import List, Optional, Dict, Any
import traceback

# ======= CONFIGURATION ======= #
CONFIG_FILE = "config.json"
ARRAYS_OUTPUT_FILE = "arrays.json"
LOG_FILE = "bingo_processor.log"

# Default configuration
DEFAULT_CONFIG = {
    "tesseract_path": r'C:/Program Files/Tesseract-OCR/tesseract.exe',
    "poppler_path": r'./poppler-24.08.0/Library\bin',
    "pdf_file": "bingo.pdf",
    "dpi": 400,
    "grid_boundaries": {
        "top": 0.25,
        "bottom": 0.85,
        "left": 0.10,
        "right": 0.90
    },
    "ocr_settings": {
        "scaling_factors": [2.0, 3.0, 4.0],
        "psm_modes": [8, 10, 6],
        "contrast_enhancement": 2.0,
        "sharpness_enhancement": 2.0
    },
    "validation": {
        "min_number": 1,
        "max_number": 75,
        "enable_strict_validation": True
    }
}
# ============================= #

def setup_logging() -> logging.Logger:
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(LOG_FILE),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def load_config() -> Dict[str, Any]:
    """Load configuration from file or create default"""
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r') as f:
                config = json.load(f)
            # Merge with defaults to ensure all keys exist
            merged_config = DEFAULT_CONFIG.copy()
            merged_config.update(config)
            return merged_config
        else:
            # Create default config file
            save_config(DEFAULT_CONFIG)
            return DEFAULT_CONFIG.copy()
    except Exception as e:
        logger.warning(f"Error loading config: {e}. Using defaults.")
        return DEFAULT_CONFIG.copy()

def save_config(config: Dict[str, Any]) -> None:
    """Save configuration to file"""
    try:
        with open(CONFIG_FILE, 'w') as f:
            json.dump(config, f, indent=4)
    except Exception as e:
        logger.error(f"Error saving config: {e}")

def setup_paths(config: Dict[str, Any]) -> None:
    """Setup tesseract and poppler paths"""
    try:
        pytesseract.pytesseract.tesseract_cmd = config['tesseract_path']
        os.environ["PATH"] += os.pathsep + os.path.dirname(config['tesseract_path'])
        os.environ["PATH"] += os.pathsep + config['poppler_path']
        logger.info("Paths configured successfully")
    except Exception as e:
        logger.error(f"Error setting up paths: {e}")
        raise

# Initialize logging
logger = setup_logging()
config = load_config()
setup_paths(config)

def preprocess_for_clean_text(image) -> List[np.ndarray]:
    """Multiple preprocessing strategies optimized for clean digital text"""
    try:
        img_array = np.array(image)

        # Convert to grayscale if needed
        if len(img_array.shape) == 3:
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
        else:
            gray = img_array

        processed_images = []

        # Strategy 1: Minimal processing (best for clean text)
        # Just ensure proper contrast
        clean = gray.copy()
        # Ensure black text on white background
        if np.mean(clean) < 128:
            clean = cv2.bitwise_not(clean)
        processed_images.append(clean)

        # Strategy 2: Simple thresholding
        _, thresh_simple = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        if np.mean(thresh_simple) < 128:
            thresh_simple = cv2.bitwise_not(thresh_simple)
        processed_images.append(thresh_simple)

        # Strategy 3: Adaptive thresholding (for varying lighting)
        adaptive = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        if np.mean(adaptive) < 128:
            adaptive = cv2.bitwise_not(adaptive)
        processed_images.append(adaptive)

        # Strategy 4: Enhanced contrast version
        enhanced = cv2.convertScaleAbs(gray, alpha=1.5, beta=0)
        _, thresh_enhanced = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        if np.mean(thresh_enhanced) < 128:
            thresh_enhanced = cv2.bitwise_not(thresh_enhanced)
        processed_images.append(thresh_enhanced)

        return processed_images

    except Exception as e:
        logger.error(f"Error in preprocess_for_clean_text: {e}")
        return []

def enhance_image(image) -> Optional[np.ndarray]:
    """Apply PIL-based image enhancement with configurable settings"""
    try:
        # Convert to PIL image
        pil_img = Image.fromarray(image)

        # Enhance contrast
        enhancer = ImageEnhance.Contrast(pil_img)
        enhanced = enhancer.enhance(config['ocr_settings']['contrast_enhancement'])

        # Enhance sharpness
        enhancer = ImageEnhance.Sharpness(enhanced)
        enhanced = enhancer.enhance(config['ocr_settings']['sharpness_enhancement'])

        # Convert back to numpy array
        return np.array(enhanced)
    except Exception as e:
        logger.error(f"Error in enhance_image: {e}")
        return None

def validate_bingo_number(number: int) -> bool:
    """Validate if number is in valid bingo range"""
    min_num = config['validation']['min_number']
    max_num = config['validation']['max_number']
    return min_num <= number <= max_num

def extract_number_from_cell(cell_img) -> Optional[int]:
    """Try multiple methods to extract number from a cell with enhanced validation"""
    try:
        # Preprocess the image
        processed = preprocess_image(cell_img)
        if processed is None:
            return None

        # Get configuration values
        scaling_factors = config['ocr_settings']['scaling_factors']
        psm_modes = config['ocr_settings']['psm_modes']

        best_result = None
        valid_results = []

        for scale in scaling_factors:
            try:
                scaled = Image.fromarray(processed).resize(
                    (int(cell_img.width * scale), int(cell_img.height * scale)),
                    Image.LANCZOS
                )
                enhanced = enhance_image(np.array(scaled))
                if enhanced is None:
                    continue

                for psm in psm_modes:
                    try:
                        text = pytesseract.image_to_string(
                            Image.fromarray(enhanced),
                            config=f'--psm {psm} --oem 3 -c tessedit_char_whitelist=0123456789'
                        ).strip()

                        # Extract digits from text
                        digits = re.findall(r'\d+', text)
                        if digits:
                            number = int(digits[0])

                            # Validate number
                            if validate_bingo_number(number):
                                valid_results.append(number)
                                return number  # Return first valid result
                            elif best_result is None:
                                best_result = number
                    except Exception as e:
                        logger.debug(f"OCR attempt failed: {e}")
                        continue
            except Exception as e:
                logger.debug(f"Scaling failed for factor {scale}: {e}")
                continue

        # If strict validation is disabled, return best result even if invalid
        if not config['validation']['enable_strict_validation'] and best_result is not None:
            return best_result

        return None
    except Exception as e:
        logger.error(f"Error in extract_number_from_cell: {e}")
        return None

def extract_grid(image) -> Optional[List[List[Optional[int]]]]:
    """Extract 5x5 bingo grid from the image with enhanced error handling"""
    try:
        # Get grid boundaries from configuration
        boundaries = config['grid_boundaries']
        grid_top = int(boundaries['top'] * image.height)
        grid_bottom = int(boundaries['bottom'] * image.height)
        grid_left = int(boundaries['left'] * image.width)
        grid_right = int(boundaries['right'] * image.width)

        logger.info(f"Grid boundaries: top={grid_top}, bottom={grid_bottom}, left={grid_left}, right={grid_right}")

        # Crop the grid area
        grid_img = image.crop((grid_left, grid_top, grid_right, grid_bottom))

        # Calculate cell dimensions
        cell_width = (grid_right - grid_left) // 5
        cell_height = (grid_bottom - grid_top) // 5

        logger.info(f"Cell dimensions: width={cell_width}, height={cell_height}")

        grid = []
        failed_cells = []

        # Process each cell in the 5x5 grid
        for row in range(5):
            row_numbers = []
            for col in range(5):
                # Skip center cell (free space)
                if row == 2 and col == 2:
                    row_numbers.append(0)  # Free space
                    continue

                # Define cell boundaries
                left = col * cell_width
                top = row * cell_height
                right = left + cell_width
                bottom = top + cell_height

                try:
                    # Crop the cell
                    cell = grid_img.crop((left, top, right, bottom))

                    # Extract number with enhanced processing
                    number = extract_number_from_cell(cell)
                    row_numbers.append(number)

                    if number is None:
                        failed_cells.append((row, col))
                        logger.warning(f"Failed to extract number from cell ({row}, {col})")
                except Exception as e:
                    logger.error(f"Error processing cell ({row}, {col}): {e}")
                    row_numbers.append(None)
                    failed_cells.append((row, col))

            grid.append(row_numbers)

        if failed_cells:
            logger.warning(f"Failed to extract {len(failed_cells)} cells: {failed_cells}")

        return grid
    except Exception as e:
        logger.error(f"Error in extract_grid: {e}")
        return None

def get_bingo_boards(pdf_path: str) -> List[Image.Image]:
    """Convert PDF to images with enhanced error handling"""
    if not os.path.exists(pdf_path):
        logger.error(f"PDF file not found at {os.path.abspath(pdf_path)}")
        return []

    try:
        logger.info(f"Converting PDF: {pdf_path}")
        images = convert_from_path(
            pdf_path,
            poppler_path=config['poppler_path'],
            dpi=config['dpi'],
            grayscale=True,
            fmt='jpeg',
            thread_count=4
        )
        logger.info(f"Successfully converted {len(images)} pages from PDF")
        return images
    except Exception as e:
        logger.error(f"PDF Conversion Error: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return []

def save_arrays_to_json(all_boards: List[List[List[Optional[int]]]], filename: str = None) -> bool:
    """Save extracted bingo arrays to JSON file"""
    try:
        if filename is None:
            filename = ARRAYS_OUTPUT_FILE

        # Prepare data structure
        output_data = {
            "timestamp": datetime.now().isoformat(),
            "total_boards": len(all_boards),
            "boards": []
        }

        for i, board in enumerate(all_boards):
            board_data = {
                "board_number": i + 1,
                "grid": board,
                "statistics": {
                    "total_cells": 25,
                    "free_space": 1,
                    "extracted_numbers": sum(1 for row in board for cell in row if cell is not None and cell != 0),
                    "failed_extractions": sum(1 for row in board for cell in row if cell is None),
                    "success_rate": round((sum(1 for row in board for cell in row if cell is not None and cell != 0) / 24) * 100, 2)
                }
            }
            output_data["boards"].append(board_data)

        # Calculate overall statistics
        total_cells = len(all_boards) * 24  # 24 non-free cells per board
        successful_extractions = sum(board_data["statistics"]["extracted_numbers"] for board_data in output_data["boards"])

        output_data["overall_statistics"] = {
            "total_cells_to_extract": total_cells,
            "successful_extractions": successful_extractions,
            "failed_extractions": total_cells - successful_extractions,
            "overall_success_rate": round((successful_extractions / total_cells) * 100, 2) if total_cells > 0 else 0
        }

        # Save to file
        with open(filename, 'w') as f:
            json.dump(output_data, f, indent=4)

        logger.info(f"Successfully saved {len(all_boards)} boards to {filename}")
        logger.info(f"Overall success rate: {output_data['overall_statistics']['overall_success_rate']}%")
        return True

    except Exception as e:
        logger.error(f"Error saving arrays to JSON: {e}")
        return False

def load_arrays_from_json(filename: str = None) -> Optional[Dict[str, Any]]:
    """Load bingo arrays from JSON file"""
    try:
        if filename is None:
            filename = ARRAYS_OUTPUT_FILE

        if not os.path.exists(filename):
            logger.warning(f"JSON file not found: {filename}")
            return None

        with open(filename, 'r') as f:
            data = json.load(f)

        logger.info(f"Successfully loaded data from {filename}")
        return data

    except Exception as e:
        logger.error(f"Error loading arrays from JSON: {e}")
        return None

def print_board_summary(board: List[List[Optional[int]]], board_num: int) -> None:
    """Print a formatted summary of a bingo board"""
    print(f"\nBoard {board_num}:")
    print("  B    I    N    G    O")
    print("  " + "-" * 20)

    for i, row in enumerate(board):
        row_str = "  "
        for j, cell in enumerate(row):
            if cell is None:
                row_str += "--   "
            elif cell == 0:  # Free space
                row_str += "FREE "
            else:
                row_str += f"{cell:2d}   "
        print(row_str)

    # Calculate statistics
    valid_numbers = [cell for row in board for cell in row if cell is not None and cell != 0]
    failed_count = sum(1 for row in board for cell in row if cell is None)

    print(f"  Valid numbers extracted: {len(valid_numbers)}/24")
    if failed_count > 0:
        print(f"  Failed extractions: {failed_count}")
    print(f"  Success rate: {(len(valid_numbers)/24)*100:.1f}%")

def main():
    """Main execution function with enhanced error handling and JSON storage"""
    try:
        logger.info("Starting Bingo Grid Extractor")
        print("\n=== Enhanced Bingo Grid Extractor ===")
        print(f"Configuration loaded from: {CONFIG_FILE}")
        print(f"Output will be saved to: {ARRAYS_OUTPUT_FILE}")

        # Get PDF file path from config
        pdf_file = config['pdf_file']

        # Get images from PDF
        logger.info(f"Processing PDF file: {pdf_file}")
        images = get_bingo_boards(pdf_file)

        if not images:
            logger.error("No images processed. Exiting.")
            print("❌ No images could be processed from the PDF file.")
            return False

        print(f"✅ Successfully loaded {len(images)} pages from PDF")

        all_boards = []
        successful_boards = 0

        for i, img in enumerate(images):
            logger.info(f"Processing page {i+1}/{len(images)}")
            print(f"\n📄 Processing Page {i+1}...")

            grid = extract_grid(img)

            if grid is None:
                logger.error(f"Failed to extract grid from page {i+1}")
                print(f"❌ Failed to process page {i+1}")
                continue

            all_boards.append(grid)

            # Print formatted board
            print_board_summary(grid, i+1)

            # Check if extraction was successful
            valid_numbers = sum(1 for row in grid for cell in row if cell is not None and cell != 0)
            if valid_numbers >= 20:  # At least 20/24 cells extracted
                successful_boards += 1
                print("✅ Board extraction successful")
            else:
                print("⚠️  Board extraction partially failed")

            # Save debug images if any cell couldn't be parsed
            failed_cells = sum(1 for row in grid for cell in row if cell is None)
            if failed_cells > 0:
                try:
                    debug_img = img.copy()
                    debug_filename = f"debug_page_{i+1}.jpg"
                    debug_img.save(debug_filename)
                    logger.info(f"Saved debug image: {debug_filename}")
                    print(f"🔍 Debug image saved: {debug_filename}")
                except Exception as e:
                    logger.error(f"Failed to save debug image: {e}")

        if not all_boards:
            logger.error("No boards were successfully extracted")
            print("❌ No boards could be extracted from any pages.")
            return False

        # Save results to JSON
        print(f"\n💾 Saving results to {ARRAYS_OUTPUT_FILE}...")
        if save_arrays_to_json(all_boards):
            print("✅ Results successfully saved to JSON file")
        else:
            print("❌ Failed to save results to JSON file")

        # Print final summary
        print("\n=== Final Summary ===")
        print(f"📊 Total pages processed: {len(images)}")
        print(f"📋 Boards extracted: {len(all_boards)}")
        print(f"✅ Successful boards: {successful_boards}")
        print(f"📁 Results saved to: {ARRAYS_OUTPUT_FILE}")
        print(f"📝 Log file: {LOG_FILE}")

        if successful_boards == len(all_boards):
            print("🎉 All boards processed successfully!")
        elif successful_boards > 0:
            print(f"⚠️  {len(all_boards) - successful_boards} boards had extraction issues")
        else:
            print("❌ All boards had significant extraction issues")

        logger.info("Bingo Grid Extractor completed successfully")
        return True

    except Exception as e:
        logger.error(f"Fatal error in main execution: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        print(f"❌ Fatal error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)
