# Enhanced Bingo Image Processor

A robust, feature-rich Python application for extracting bingo numbers from PDF files using OCR technology. The enhanced version includes comprehensive error handling, JSON storage, configuration management, and analysis tools.

## 🚀 Features

### Core Functionality
- **PDF to Image Conversion**: Converts PDF pages to high-quality images
- **Advanced OCR Processing**: Multiple OCR strategies for maximum accuracy
- **5x5 Grid Extraction**: Automatically detects and processes bingo grids
- **JSON Storage**: Saves extracted arrays to structured JSON files
- **Comprehensive Logging**: Detailed logging for debugging and monitoring

### Enhanced Features
- **Configuration Management**: Customizable settings via JSON config file
- **Error Handling**: Robust error handling with detailed logging
- **Progress Tracking**: Real-time progress updates with emoji indicators
- **Debug Images**: Automatic saving of debug images for failed extractions
- **Statistics**: Detailed success rate and extraction statistics
- **Validation**: Configurable number validation (1-75 range)

### Analysis Tools
- **Array Viewer**: Interactive utility to view and analyze extracted data
- **Pattern Analysis**: Statistical analysis of extracted numbers
- **CSV Export**: Export data to CSV format for further analysis
- **Success Rate Tracking**: Monitor extraction performance over time

## 📁 File Structure

```
BIngo Image Processor/
├── Image_processor.py      # Main processing script
├── array_viewer.py         # Interactive data viewer utility
├── config.json            # Configuration file
├── arrays.json            # Output file with extracted arrays
├── bingo_processor.log     # Log file
├── debug_page_*.jpg       # Debug images (auto-generated)
├── poppler-24.08.0/       # Poppler PDF processing library
└── README.md              # This file
```

## 🛠️ Installation

### Prerequisites
- Python 3.7+
- Tesseract OCR
- Required Python packages

### Required Python Packages
```bash
pip install opencv-python
pip install numpy
pip install pytesseract
pip install pdf2image
pip install Pillow
```

### Tesseract OCR Installation
1. Download and install Tesseract OCR from: https://github.com/tesseract-ocr/tesseract
2. Update the `tesseract_path` in `config.json` if installed in a different location

## ⚙️ Configuration

The application uses a `config.json` file for configuration. Key settings include:

```json
{
    "tesseract_path": "C:/Program Files/Tesseract-OCR/tesseract.exe",
    "poppler_path": "./poppler-24.08.0/Library\\bin",
    "pdf_file": "bingo.pdf",
    "dpi": 400,
    "grid_boundaries": {
        "top": 0.25,
        "bottom": 0.85,
        "left": 0.10,
        "right": 0.90
    },
    "ocr_settings": {
        "scaling_factors": [2.0, 3.0, 4.0],
        "psm_modes": [8, 10, 6],
        "contrast_enhancement": 2.0,
        "sharpness_enhancement": 2.0
    },
    "validation": {
        "min_number": 1,
        "max_number": 75,
        "enable_strict_validation": true
    }
}
```

### Configuration Options

- **tesseract_path**: Path to Tesseract OCR executable
- **poppler_path**: Path to Poppler PDF processing library
- **pdf_file**: Input PDF file name
- **dpi**: Image resolution for PDF conversion (higher = better quality)
- **grid_boundaries**: Relative positions of the bingo grid (0.0-1.0)
- **ocr_settings**: OCR processing parameters
- **validation**: Number validation settings

## 🚀 Usage

### Basic Usage

1. Place your PDF file in the project directory
2. Update the `pdf_file` setting in `config.json`
3. Run the main processor:

```bash
python Image_processor.py
```

### Using the Array Viewer

To view and analyze extracted data:

```bash
python array_viewer.py
```

The viewer provides an interactive menu with options to:
- View summary statistics
- Display all boards
- View specific boards
- Analyze number patterns
- Export to CSV format

## 📊 Output Format

### JSON Structure

The extracted data is saved in `arrays.json` with the following structure:

```json
{
    "timestamp": "2025-07-07T08:50:00.919904",
    "total_boards": 1,
    "boards": [
        {
            "board_number": 1,
            "grid": [
                [4, 23, null, 58, 68],
                [7, 18, 39, 8, 70],
                [18, 8, 0, 52, 8],
                [2, 30, 32, null, 69],
                [1, 28, 48, null, 62]
            ],
            "statistics": {
                "total_cells": 25,
                "free_space": 1,
                "extracted_numbers": 21,
                "failed_extractions": 3,
                "success_rate": 87.5
            }
        }
    ],
    "overall_statistics": {
        "total_cells_to_extract": 24,
        "successful_extractions": 21,
        "failed_extractions": 3,
        "overall_success_rate": 87.5
    }
}
```

### Data Interpretation

- **null**: Failed to extract number from cell
- **0**: Free space (center cell)
- **Numbers 1-75**: Successfully extracted bingo numbers
- **Statistics**: Success rates and extraction counts

## 🔧 Troubleshooting

### Common Issues

1. **Tesseract not found**
   - Verify Tesseract installation
   - Check `tesseract_path` in config.json

2. **PDF conversion fails**
   - Ensure Poppler is properly installed
   - Check `poppler_path` in config.json

3. **Low extraction accuracy**
   - Adjust `grid_boundaries` for your PDF layout
   - Modify OCR settings (scaling factors, PSM modes)
   - Increase DPI for better image quality

4. **Numbers outside valid range**
   - Check `validation` settings
   - Disable strict validation if needed

### Debug Features

- **Debug Images**: Automatically saved for failed extractions
- **Detailed Logging**: Check `bingo_processor.log` for detailed information
- **Progress Indicators**: Real-time feedback during processing

## 📈 Performance Tips

1. **Optimize DPI**: Higher DPI improves accuracy but increases processing time
2. **Adjust Grid Boundaries**: Fine-tune for your specific PDF layout
3. **OCR Settings**: Experiment with different scaling factors and PSM modes
4. **Batch Processing**: Process multiple PDFs by updating config between runs

## 🤝 Contributing

Feel free to submit issues, feature requests, or pull requests to improve the application.

## 📝 License

This project is open source and available under the MIT License.

## 🔄 Version History

### v2.0 (Enhanced Version)
- Added JSON storage functionality
- Implemented configuration management
- Enhanced error handling and logging
- Added interactive array viewer
- Improved OCR accuracy with multiple strategies
- Added statistics and pattern analysis
- Implemented CSV export functionality
- Added debug image generation

### v1.0 (Original Version)
- Basic PDF to bingo grid extraction
- Simple console output
- Limited error handling

---

## 📞 Support

For support or questions, please check the log files and debug images first. The application provides comprehensive logging to help diagnose issues.
