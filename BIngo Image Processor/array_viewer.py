#!/usr/bin/env python3
"""
Bingo Array Viewer - Utility to view and analyze stored bingo arrays
"""

import json
import os
import sys
from datetime import datetime
from typing import Dict, Any, List, Optional

def load_arrays(filename: str = "arrays.json") -> Optional[Dict[str, Any]]:
    """Load bingo arrays from JSON file"""
    try:
        if not os.path.exists(filename):
            print(f"❌ File not found: {filename}")
            return None
            
        with open(filename, 'r') as f:
            data = json.load(f)
            
        print(f"✅ Successfully loaded data from {filename}")
        return data
        
    except Exception as e:
        print(f"❌ Error loading arrays: {e}")
        return None

def print_board(board_data: Dict[str, Any]) -> None:
    """Print a formatted bingo board"""
    board_num = board_data['board_number']
    grid = board_data['grid']
    stats = board_data['statistics']
    
    print(f"\n🎯 Board {board_num}:")
    print("   B    I    N    G    O")
    print("   " + "─" * 20)
    
    for i, row in enumerate(grid):
        row_str = "   "
        for j, cell in enumerate(row):
            if cell is None:
                row_str += "--   "
            elif cell == 0:  # Free space
                row_str += "FREE "
            else:
                row_str += f"{cell:2d}   "
        print(row_str)
    
    print(f"   📊 Success Rate: {stats['success_rate']}%")
    print(f"   ✅ Extracted: {stats['extracted_numbers']}/24")
    if stats['failed_extractions'] > 0:
        print(f"   ❌ Failed: {stats['failed_extractions']}")

def print_summary(data: Dict[str, Any]) -> None:
    """Print overall summary"""
    print("\n" + "="*50)
    print("📋 BINGO EXTRACTION SUMMARY")
    print("="*50)
    
    timestamp = datetime.fromisoformat(data['timestamp'])
    print(f"🕒 Processed: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📄 Total Boards: {data['total_boards']}")
    
    overall = data['overall_statistics']
    print(f"📊 Overall Success Rate: {overall['overall_success_rate']}%")
    print(f"✅ Successful Extractions: {overall['successful_extractions']}")
    print(f"❌ Failed Extractions: {overall['failed_extractions']}")
    print(f"🎯 Total Cells Processed: {overall['total_cells_to_extract']}")

def analyze_patterns(data: Dict[str, Any]) -> None:
    """Analyze patterns in the extracted data"""
    print("\n" + "="*50)
    print("🔍 PATTERN ANALYSIS")
    print("="*50)
    
    all_numbers = []
    column_numbers = [[], [], [], [], []]  # B, I, N, G, O
    
    for board_data in data['boards']:
        grid = board_data['grid']
        for row_idx, row in enumerate(grid):
            for col_idx, cell in enumerate(row):
                if cell is not None and cell != 0:
                    all_numbers.append(cell)
                    column_numbers[col_idx].append(cell)
    
    if all_numbers:
        print(f"📈 Number Range: {min(all_numbers)} - {max(all_numbers)}")
        print(f"🔢 Unique Numbers: {len(set(all_numbers))}")
        print(f"📊 Average: {sum(all_numbers)/len(all_numbers):.1f}")
        
        # Column analysis
        columns = ['B', 'I', 'N', 'G', 'O']
        print("\n📋 Column Analysis:")
        for i, (col_name, numbers) in enumerate(zip(columns, column_numbers)):
            if numbers:
                print(f"   {col_name}: {min(numbers)}-{max(numbers)} (avg: {sum(numbers)/len(numbers):.1f})")

def export_to_csv(data: Dict[str, Any], filename: str = "bingo_boards.csv") -> None:
    """Export boards to CSV format"""
    try:
        import csv
        
        with open(filename, 'w', newline='') as csvfile:
            writer = csv.writer(csvfile)
            
            # Header
            writer.writerow(['Board', 'B1', 'I1', 'N1', 'G1', 'O1',
                           'B2', 'I2', 'N2', 'G2', 'O2',
                           'B3', 'I3', 'N3', 'G3', 'O3',
                           'B4', 'I4', 'N4', 'G4', 'O4',
                           'B5', 'I5', 'N5', 'G5', 'O5',
                           'Success_Rate'])
            
            # Data rows
            for board_data in data['boards']:
                row = [board_data['board_number']]
                grid = board_data['grid']
                
                for grid_row in grid:
                    for cell in grid_row:
                        if cell is None:
                            row.append('')
                        elif cell == 0:
                            row.append('FREE')
                        else:
                            row.append(cell)
                
                row.append(board_data['statistics']['success_rate'])
                writer.writerow(row)
        
        print(f"✅ Exported to CSV: {filename}")
        
    except Exception as e:
        print(f"❌ Error exporting to CSV: {e}")

def main():
    """Main function with interactive menu"""
    if len(sys.argv) > 1:
        filename = sys.argv[1]
    else:
        filename = "arrays.json"
    
    data = load_arrays(filename)
    if not data:
        return
    
    while True:
        print("\n" + "="*50)
        print("🎯 BINGO ARRAY VIEWER")
        print("="*50)
        print("1. 📋 Show Summary")
        print("2. 🎯 View All Boards")
        print("3. 🔍 View Specific Board")
        print("4. 📊 Pattern Analysis")
        print("5. 📁 Export to CSV")
        print("6. 🚪 Exit")
        print("="*50)
        
        try:
            choice = input("Select option (1-6): ").strip()
            
            if choice == '1':
                print_summary(data)
                
            elif choice == '2':
                for board_data in data['boards']:
                    print_board(board_data)
                    
            elif choice == '3':
                board_num = int(input("Enter board number: "))
                found = False
                for board_data in data['boards']:
                    if board_data['board_number'] == board_num:
                        print_board(board_data)
                        found = True
                        break
                if not found:
                    print(f"❌ Board {board_num} not found")
                    
            elif choice == '4':
                analyze_patterns(data)
                
            elif choice == '5':
                csv_filename = input("Enter CSV filename (default: bingo_boards.csv): ").strip()
                if not csv_filename:
                    csv_filename = "bingo_boards.csv"
                export_to_csv(data, csv_filename)
                
            elif choice == '6':
                print("👋 Goodbye!")
                break
                
            else:
                print("❌ Invalid choice. Please select 1-6.")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except ValueError:
            print("❌ Invalid input. Please enter a number.")
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
