//========================================================================
//
// PDFDocBuilder.h
//
// This file is licensed under the GPLv2 or later
//
// Copyright 2010 Hib Eris <<EMAIL>>
// Copyright 2010, 2018, 2020, 2022 Albert <PERSON>tals Cid <<EMAIL>>
// Copyright 2021 <PERSON> <<EMAIL>>
//
//========================================================================

#ifndef PDFDOCBUILDER_H
#define PDFDOCBUILDER_H

#include <memory>

#include "PDFDoc.h"
class GooString;

//------------------------------------------------------------------------
// PDFDocBuilder
//
// PDFDocBuilder is an abstract class that specifies the interface for
// constructing PDFDocs.
//------------------------------------------------------------------------

class PDFDocBuilder
{

public:
    PDFDocBuilder() = default;
    virtual ~PDFDocBuilder();

    PDFDocBuilder(const PDFDocBuilder &) = delete;
    PDFDocBuilder &operator=(const PDFDocBuilder &) = delete;

    // Builds a new PDFDoc. Returns a PDFDoc. You should check this PDFDoc
    // with PDFDoc::isOk() for failures.
    // The caller is responsible for deleting ownerPassword, userPassWord and guiData.
    virtual std::unique_ptr<PDFDoc> buildPDFDoc(const GooString &uri, const std::optional<GooString> &ownerPassword = {}, const std::optional<GooString> &userPassword = {}, void *guiDataA = nullptr) = 0;

    // Returns true if the builder supports building a PDFDoc from the URI.
    virtual bool supports(const GooString &uri) = 0;
};

#endif /* PDFDOCBUILDER_H */
