//========================================================================
//
// FileDescriptorPDFDocBuilder.h
//
// This file is licensed under the GPLv2 or later
//
// Copyright 2010 Hib Eris <<EMAIL>>
// Copyright 2010, 2018, 2022 Albert <PERSON>tals Cid <<EMAIL>>
// Copyright 2021 Oliver <PERSON> <<EMAIL>>
// Copyright 2021 Christian <PERSON> <<EMAIL>>
//
//========================================================================

#ifndef FDPDFDOCBUILDER_H
#define FDPDFDOCBUILDER_H

#include "PDFDocBuilder.h"

//------------------------------------------------------------------------
// FileDescriptorPDFDocBuilder
//
// The FileDescriptorPDFDocBuilder implements a PDFDocBuilder that read from a file descriptor.
//------------------------------------------------------------------------

class FileDescriptorPDFDocBuilder : public PDFDocBuilder
{

public:
    std::unique_ptr<PDFDoc> buildPDFDoc(const GooString &uri, const std::optional<GooString> &ownerPassword = {}, const std::optional<GooString> &userPassword = {}, void *guiDataA = nullptr) override;
    bool supports(const GooString &uri) override;

private:
    int parseFdFromUri(const GooString &uri);
};

#endif /* FDPDFDOCBUILDER_H */
