										-*- text -*-
poppler-data

This package consists of encoding files for use with poppler.  The
encoding files are optional and pop<PERSON> will automatically read them
if they are present.  When installed, the encoding files enables
pop<PERSON> to correctly render CJK and Cyrillic properly.  While poppler
is licensed under the GPL, these encoding files have different license, 
and thus distributed separately.

All the Makefile does is install the data files, so autotools seemed a
bit overkill.  There is no ./configure file, all there is to do is to
say 'make install'.  To override the install destinations, specify
prefix, datadir, pkgdatadir or DESTDIR at 'make install' time as
desired.  For example:

	$ make install datadir=/usr DESTDIR=/tmp/buildroot-xyz2000

will install the files under /usr/share/poppler in a temporary build
root.

                                                <PERSON><PERSON> <<EMAIL>>
                                                Updated by: <PERSON> <<EMAIL>>
