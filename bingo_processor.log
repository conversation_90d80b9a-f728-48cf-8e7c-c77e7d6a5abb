2025-07-07 08:49:46,328 - INFO - Paths configured successfully
2025-07-07 08:49:46,329 - INFO - Starting Bingo Grid Extractor
2025-07-07 08:49:46,330 - INFO - Processing PDF file: bingo.pdf
2025-07-07 08:49:46,330 - INFO - Converting PDF: bingo.pdf
2025-07-07 08:49:46,872 - INFO - Successfully converted 1 pages from PDF
2025-07-07 08:49:46,872 - INFO - Processing page 1/1
2025-07-07 08:49:46,873 - INFO - Grid boundaries: top=1169, bottom=3976, left=330, right=2977
2025-07-07 08:49:46,920 - INFO - Cell dimensions: width=529, height=561
2025-07-07 08:49:49,120 - WARNING - Failed to extract number from cell (0, 2)
2025-07-07 08:49:57,326 - WARNING - Failed to extract number from cell (3, 3)
2025-07-07 08:49:59,955 - WARNING - Failed to extract number from cell (4, 3)
2025-07-07 08:50:00,833 - WARNING - Failed to extract 3 cells: [(0, 2), (3, 3), (4, 3)]
2025-07-07 08:50:00,919 - INFO - Saved debug image: debug_page_1.jpg
2025-07-07 08:50:00,921 - INFO - Successfully saved 1 boards to arrays.json
2025-07-07 08:50:00,921 - INFO - Overall success rate: 87.5%
2025-07-07 08:50:00,923 - INFO - Bingo Grid Extractor completed successfully
2025-07-07 08:54:53,998 - INFO - Paths configured successfully
2025-07-07 08:54:53,998 - INFO - Starting Bingo Grid Extractor
2025-07-07 08:54:53,998 - INFO - Processing PDF file: bingo.pdf
2025-07-07 08:54:53,998 - INFO - Converting PDF: bingo.pdf
2025-07-07 08:54:54,669 - INFO - Successfully converted 1 pages from PDF
2025-07-07 08:54:54,670 - INFO - Processing page 1/1
2025-07-07 08:54:54,670 - INFO - Grid boundaries: top=1169, bottom=3976, left=330, right=2977
2025-07-07 08:54:54,729 - INFO - Cell dimensions: width=529, height=561
2025-07-07 08:54:57,310 - WARNING - Failed to extract number from cell (0, 2)
2025-07-07 08:55:05,786 - WARNING - Failed to extract number from cell (3, 3)
2025-07-07 08:55:09,067 - WARNING - Failed to extract number from cell (4, 3)
2025-07-07 08:55:09,927 - WARNING - Failed to extract 3 cells: [(0, 2), (3, 3), (4, 3)]
2025-07-07 08:55:09,977 - INFO - Saved debug image: debug_page_1.jpg
2025-07-07 08:55:09,978 - INFO - Successfully saved 1 boards to arrays.json
2025-07-07 08:55:09,978 - INFO - Overall success rate: 87.5%
2025-07-07 08:55:09,978 - INFO - Bingo Grid Extractor completed successfully
2025-07-07 09:13:08,620 - INFO - Paths configured successfully
2025-07-07 09:13:08,620 - INFO - Starting Bingo Grid Extractor
2025-07-07 09:13:08,621 - INFO - Processing PDF file: bingo.pdf
2025-07-07 09:13:08,621 - INFO - Converting PDF: bingo.pdf
2025-07-07 09:13:09,668 - INFO - Successfully converted 1 pages from PDF
2025-07-07 09:13:09,668 - INFO - Processing page 1/1
2025-07-07 09:13:09,669 - INFO - Grid boundaries: top=1754, bottom=5963, left=496, right=4464
2025-07-07 09:13:09,755 - INFO - Cell dimensions: width=793, height=841
2025-07-07 09:16:01,785 - INFO - Paths configured successfully
2025-07-07 09:16:01,786 - INFO - Starting Bingo Grid Extractor
2025-07-07 09:16:01,787 - INFO - Processing PDF file: bingo.pdf
2025-07-07 09:16:01,787 - INFO - Converting PDF: bingo.pdf
2025-07-07 09:16:02,986 - INFO - Successfully converted 1 pages from PDF
2025-07-07 09:16:02,987 - INFO - Processing page 1/1
2025-07-07 09:16:02,988 - INFO - Grid boundaries: top=1754, bottom=5963, left=496, right=4464
2025-07-07 09:16:03,127 - INFO - Cell dimensions: width=793, height=841
2025-07-07 09:16:39,089 - WARNING - Failed to extract number from cell (1, 0)
2025-07-07 09:17:27,793 - WARNING - Failed to extract number from cell (2, 3)
2025-07-07 09:17:46,178 - WARNING - Failed to extract number from cell (3, 0)
2025-07-07 09:18:31,359 - WARNING - Failed to extract 3 cells: [(1, 0), (2, 3), (3, 0)]
2025-07-07 09:18:31,545 - INFO - Saved debug image: debug_page_1.jpg
2025-07-07 09:18:31,547 - INFO - Successfully saved 1 boards to arrays.json
2025-07-07 09:18:31,547 - INFO - Overall success rate: 87.5%
2025-07-07 09:18:31,548 - INFO - Bingo Grid Extractor completed successfully
2025-07-07 09:20:55,067 - INFO - Paths configured successfully
2025-07-07 09:20:55,067 - INFO - Starting Bingo Grid Extractor
2025-07-07 09:20:55,068 - INFO - Processing PDF file: bingo.pdf
2025-07-07 09:20:55,068 - INFO - Converting PDF: bingo.pdf
2025-07-07 09:20:56,141 - INFO - Successfully converted 1 pages from PDF
2025-07-07 09:20:56,141 - INFO - Processing page 1/1
2025-07-07 09:21:33,642 - WARNING - Failed to extract number from cell (1, 0)
2025-07-07 09:22:27,784 - WARNING - Failed to extract number from cell (2, 3)
2025-07-07 09:22:49,370 - WARNING - Failed to extract number from cell (3, 0)
2025-07-07 09:23:32,677 - WARNING - Failed to extract 3 cells: [(1, 0), (2, 3), (3, 0)]
2025-07-07 09:23:32,679 - INFO - Strategy 'original' extracted 21/24 numbers
2025-07-07 09:23:32,679 - INFO - New best strategy: 'original' with 21 successes
2025-07-07 09:24:13,659 - WARNING - Failed to extract number from cell (1, 0)
