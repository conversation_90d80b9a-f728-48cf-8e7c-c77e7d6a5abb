#!/usr/bin/env python3
"""
Test script for the Enhanced Bingo Image Processor
"""

import os
import json
import sys
from datetime import datetime

def test_config_file():
    """Test if config file exists and is valid"""
    print("🧪 Testing configuration file...")
    
    if not os.path.exists("config.json"):
        print("❌ config.json not found")
        return False
    
    try:
        with open("config.json", 'r') as f:
            config = json.load(f)
        
        required_keys = ['tesseract_path', 'poppler_path', 'pdf_file', 'dpi', 'grid_boundaries', 'ocr_settings', 'validation']
        
        for key in required_keys:
            if key not in config:
                print(f"❌ Missing required config key: {key}")
                return False
        
        print("✅ Configuration file is valid")
        return True
        
    except Exception as e:
        print(f"❌ Error reading config file: {e}")
        return False

def test_dependencies():
    """Test if required dependencies are available"""
    print("🧪 Testing dependencies...")
    
    required_modules = [
        'cv2', 'numpy', 'pytesseract', 'pdf2image', 'PIL'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module} - OK")
        except ImportError:
            print(f"❌ {module} - MISSING")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n❌ Missing modules: {', '.join(missing_modules)}")
        print("Install with: pip install opencv-python numpy pytesseract pdf2image Pillow")
        return False
    
    print("✅ All dependencies are available")
    return True

def test_tesseract():
    """Test if Tesseract OCR is accessible"""
    print("🧪 Testing Tesseract OCR...")
    
    try:
        import pytesseract
        
        # Load config to get tesseract path
        with open("config.json", 'r') as f:
            config = json.load(f)
        
        tesseract_path = config['tesseract_path']
        
        if not os.path.exists(tesseract_path):
            print(f"❌ Tesseract not found at: {tesseract_path}")
            return False
        
        # Set tesseract path
        pytesseract.pytesseract.tesseract_cmd = tesseract_path
        
        # Test with a simple image
        from PIL import Image
        import numpy as np
        
        # Create a simple test image with text
        test_img = Image.new('RGB', (100, 50), color='white')
        
        # Try OCR
        result = pytesseract.image_to_string(test_img)
        print("✅ Tesseract OCR is working")
        return True
        
    except Exception as e:
        print(f"❌ Tesseract test failed: {e}")
        return False

def test_poppler():
    """Test if Poppler is accessible"""
    print("🧪 Testing Poppler PDF processing...")
    
    try:
        from pdf2image import convert_from_path
        
        # Load config to get poppler path
        with open("config.json", 'r') as f:
            config = json.load(f)
        
        poppler_path = config['poppler_path']
        
        if not os.path.exists(poppler_path):
            print(f"❌ Poppler not found at: {poppler_path}")
            return False
        
        print("✅ Poppler path exists")
        return True
        
    except Exception as e:
        print(f"❌ Poppler test failed: {e}")
        return False

def test_pdf_file():
    """Test if PDF file exists"""
    print("🧪 Testing PDF file...")
    
    try:
        with open("config.json", 'r') as f:
            config = json.load(f)
        
        pdf_file = config['pdf_file']
        
        if not os.path.exists(pdf_file):
            print(f"⚠️  PDF file not found: {pdf_file}")
            print("   This is normal if you haven't added your PDF yet")
            return True  # Not a critical error
        
        print(f"✅ PDF file found: {pdf_file}")
        return True
        
    except Exception as e:
        print(f"❌ Error checking PDF file: {e}")
        return False

def test_file_structure():
    """Test if all required files exist"""
    print("🧪 Testing file structure...")
    
    required_files = [
        'Image_processor.py',
        'array_viewer.py',
        'config.json'
    ]
    
    optional_files = [
        'arrays.json',
        'bingo_processor.log'
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} - OK")
        else:
            print(f"❌ {file} - MISSING")
            missing_files.append(file)
    
    for file in optional_files:
        if os.path.exists(file):
            print(f"✅ {file} - OK")
        else:
            print(f"ℹ️  {file} - Will be created when needed")
    
    if missing_files:
        print(f"\n❌ Missing required files: {', '.join(missing_files)}")
        return False
    
    print("✅ File structure is correct")
    return True

def run_all_tests():
    """Run all tests"""
    print("🚀 Enhanced Bingo Image Processor - System Test")
    print("=" * 50)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Configuration", test_config_file),
        ("Dependencies", test_dependencies),
        ("Tesseract OCR", test_tesseract),
        ("Poppler PDF", test_poppler),
        ("PDF File", test_pdf_file)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} test failed")
        except Exception as e:
            print(f"❌ {test_name} test error: {e}")
    
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 All tests passed! System is ready to use.")
        print("\nNext steps:")
        print("1. Add your PDF file to the directory")
        print("2. Update 'pdf_file' in config.json")
        print("3. Run: python Image_processor.py")
        return True
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please fix the issues above.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    if not success:
        sys.exit(1)
